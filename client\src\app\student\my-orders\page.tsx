'use client';

import React, { useState, useEffect } from 'react';
import { Package, Search, RefreshCw, Calendar, Coins } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from 'sonner';
import { isStudentAuthenticated } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import * as storePurchaseApi from '@/services/storePurchaseApi';
import Header from '@/app-components/Header';
import Footer from '@/app-components/Footer';
import Image from 'next/image';

const getImageUrl = (imagePath: string | null): string => {
  if (!imagePath) return '/logo.png';

  if (imagePath.startsWith('http')) return imagePath;

  if (imagePath.startsWith('/uploads')) {
    const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';
    const serverURL = baseURL.replace('/api/v1', '');
    return `${serverURL}${imagePath}`;
  }

  if (imagePath && !imagePath.includes('/')) {
    const baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';
    const serverURL = baseURL.replace('/api/v1', '');
    return `${serverURL}/uploads/store/${imagePath}`;
  }

  return '/logo.png';
};

const MyOrdersPage = () => {
  const [orders, setOrders] = useState<storePurchaseApi.StoreOrder[]>([]);
  const [filteredOrders, setFilteredOrders] = useState<storePurchaseApi.StoreOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const router = useRouter();

  useEffect(() => {
    if (!isStudentAuthenticated()) {
      toast.error('Please login to view your orders');
      router.push('/student/login');
      return;
    }

    loadMyOrders();
  }, [router]);

  useEffect(() => {
    let filtered = orders;

    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(order =>
        order.itemName.toLowerCase().includes(query) ||
        order.id.toLowerCase().includes(query)
      );
    }

    if (statusFilter && statusFilter !== 'all') {
      filtered = filtered.filter(order => order.status === statusFilter);
    }

    setFilteredOrders(filtered);
  }, [orders, searchQuery, statusFilter]);

  const loadMyOrders = async () => {
    try {
      setLoading(true);
      const result = await storePurchaseApi.getMyOrders();

      if (!result.success) {
        throw new Error(result.error);
      }

      const ordersData = result.data;
      setOrders(ordersData || []);
      setFilteredOrders(ordersData || []);
    } catch (error: any) {
      console.error('Error loading orders:', error);
      setOrders([]);
      setFilteredOrders([]);
      toast.error('Failed to load orders: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const OrderCard = ({ order }: { order: storePurchaseApi.StoreOrder }) => (
    <Card className="mb-4 hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex flex-col md:flex-row gap-4">
          {/* Item Image */}
          <div className="flex-shrink-0">
            {order.item?.image ? (
              <Image
                src={getImageUrl(order.item.image)}
                alt={order.itemName}
                width={80}
                height={80}
                className="rounded-lg object-cover"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/logo.png";
                }}
              />
            ) : (
              <div className="w-20 h-20 bg-gray-200 rounded-lg flex items-center justify-center">
                <Package className="w-8 h-8 text-gray-400" />
              </div>
            )}
          </div>

          {/* Order Details */}
          <div className="flex-1 space-y-2">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
              <h3 className="font-semibold text-lg text-gray-900">{order.itemName}</h3>
              <Badge className={`w-fit ${getStatusColor(order.status)}`}>
                {order.status}
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <span className="font-medium">Order ID:</span>
                <span className="font-mono">#{order.id.slice(-8)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="w-4 h-4" />
                <span>{formatDate(order.createdAt)}</span>
              </div>
              <div className="flex items-center gap-2">
                <Coins className="w-4 h-4 text-yellow-600" />
                <span className="font-medium">{order.totalCoins} coins</span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-500">Quantity:</span>
                <span className="ml-2 font-medium">{order.quantity}</span>
              </div>
              <div>
                <span className="text-gray-500">Price per item:</span>
                <span className="ml-2 font-medium">{order.itemPrice} coins</span>
              </div>
            </div>

            {order.item?.description && (
              <div className="text-sm text-gray-600">
                <span className="text-gray-500">Description:</span>
                <p className="mt-1">{order.item.description}</p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">My Orders</h1>
          <p className="text-gray-600">Track and manage your store orders</p>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search by order ID or item name..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full md:w-[200px]">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="PENDING">Pending</SelectItem>
                  <SelectItem value="COMPLETED">Completed</SelectItem>
                  <SelectItem value="CANCELLED">Cancelled</SelectItem>
                </SelectContent>
              </Select>
              <Button onClick={loadMyOrders} disabled={loading} variant="outline">
                <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Orders List */}
        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i}>
                <CardContent className="p-6">
                  <div className="flex gap-4">
                    <Skeleton className="w-20 h-20 rounded-lg" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-6 w-1/3" />
                      <Skeleton className="h-4 w-1/2" />
                      <Skeleton className="h-4 w-1/4" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : filteredOrders.length === 0 ? (
          <Card>
            <CardContent className="p-12">
              <div className="text-center">
                <Package className="w-16 h-16 mx-auto text-gray-400 mb-4" />
                <h3 className="text-xl font-medium text-gray-900 mb-2">No orders found</h3>
                <p className="text-gray-600 mb-6">
                  {orders.length === 0 
                    ? "You haven't placed any orders yet. Visit our store to start shopping!" 
                    : "No orders match your current filters."
                  }
                </p>
                {orders.length === 0 && (
                  <Button onClick={() => router.push('/store')} className="bg-[#ff914d] hover:bg-[#ff914d]/90">
                    Visit Store
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ) : (
          <div>
            <div className="mb-4 text-sm text-gray-600">
              Showing {filteredOrders.length} of {orders.length} orders
            </div>
            {filteredOrders.map((order) => (
              <OrderCard key={order.id} order={order} />
            ))}
          </div>
        )}
      </div>

      <Footer />
    </div>
  );
};

export default MyOrdersPage;
