import { Request, Response } from 'express';
import { sendSuccess, sendError } from '@/utils/response';
import * as storeService from '../services/storeService';

export const getAllStoreItems = async (req: Request, res: Response): Promise<void> => {
  try {
    const { category, status, search } = req.query;

    const items = await storeService.getAllStoreItems({
      category: category as string,
      status: status as string,
      search: search as string
    });

    sendSuccess(res, items, 'Store items retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve store items', 500);
  }
};

export const getStoreItemById = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    
    const item = await storeService.getStoreItemById(id);
    
    if (!item) {
      sendError(res, 'Store item not found', 404);
      return;
    }
    
    sendSuccess(res, item, 'Store item retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve store item', 500);
  }
};

export const createStoreItem = async (req: Request, res: Response): Promise<void> => {
  try {
    try {
      const testCount = await storeService.getAllStoreItems({});
    } catch (dbError: any) {
      sendError(res, `Database connection failed: ${dbError.message}`, 500);
      return;
    }

    const { name, description, coinPrice, quantity, category } = req.body;


    if (!name || !description || !coinPrice || quantity === undefined || quantity === null || !category) {
      sendError(res, 'Missing required fields: name, description, coinPrice, quantity, and category are required', 400);
      return;
    }

    const parsedCoinPrice = parseInt(coinPrice);
    const parsedQuantity = parseInt(quantity);

    if (isNaN(parsedCoinPrice) || parsedCoinPrice < 0) {
      sendError(res, 'coinPrice must be a valid positive number', 400);
      return;
    }

    if (isNaN(parsedQuantity) || parsedQuantity < 0) {
      sendError(res, 'quantity must be a valid non-negative number', 400);
      return;
    }

    let imagePath = null;
    if (req.file) {
      imagePath = `/uploads/store/${req.file.filename}`;
    }

    const itemData = {
      name: name.trim(),
      description: description.trim(),
      coinPrice: parsedCoinPrice,
      quantity: parsedQuantity,
      category: category.trim(),
      image: imagePath
    };

    const newItem = await storeService.createStoreItem(itemData);

    sendSuccess(res, newItem, 'Store item created successfully', 201);
  } catch (error: any) {
    sendError(res, error.message || 'Failed to create store item', 500);
  }
};

export const updateStoreItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    const { name, description, coinPrice, quantity, category, status } = req.body;

    const existingItem = await storeService.getStoreItemById(id);
    if (!existingItem) {
      sendError(res, 'Store item not found', 404);
      return;
    }

    let imagePath = existingItem.image;
    if (req.file) {
      imagePath = `/uploads/store/${req.file.filename}`;
    }

    const updatedItem = await storeService.updateStoreItem(id, {
      name,
      description,
      coinPrice: coinPrice ? parseInt(coinPrice) : undefined,
      quantity: quantity !== undefined ? parseInt(quantity) : undefined,
      category,
      image: imagePath || undefined,
      status
    });

    sendSuccess(res, updatedItem, 'Store item updated successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to update store item', 500);
  }
};

export const deleteStoreItem = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;

    const existingItem = await storeService.getStoreItemById(id);
    if (!existingItem) {
      sendError(res, 'Store item not found', 404);
      return;
    }

    await storeService.deleteStoreItem(id);

    sendSuccess(res, null, 'Store item deleted successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to delete store item', 500);
  }
};

export const getStoreStats = async (req: Request, res: Response): Promise<void> => {
  try {
    const stats = await storeService.getStoreStats();
    sendSuccess(res, stats, 'Store statistics retrieved successfully');
  } catch (error: any) {
    sendError(res, error.message || 'Failed to retrieve store statistics', 500);
  }
};
