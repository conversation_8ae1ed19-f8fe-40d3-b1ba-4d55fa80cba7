import { axiosInstance } from '@/lib/axios';
import { PurchaseData, StoreOrder } from '@/lib/types';

export type { PurchaseData, StoreOrder };

export const purchaseItems = async (data: PurchaseData) => {
  try {
    const response = await axiosInstance.post('/store/purchase', data);
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Purchase failed'
    };
  }
};

export const getMyOrders = async () => {
  try {
    const response = await axiosInstance.get('/store/orders');
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch orders'
    };
  }
};

export const getOrderDetails = async (orderId: string) => {
  try {
    const response = await axiosInstance.get(`/store/orders/${orderId}`);
    return {
      success: true,
      data: response.data.data
    };
  } catch (error: any) {
    console.error('Failed to fetch order details:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message || 'Failed to fetch order details'
    };
  }
};
